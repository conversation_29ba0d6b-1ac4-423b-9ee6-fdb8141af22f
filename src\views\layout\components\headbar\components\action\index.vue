<script setup>


import { getCurrentInstance, ref, provide, reactive, onActivated, onMounted, watch } from 'vue';
import information from '@/views/account/information.vue';
import accountInfo from '@/views/account/account_info_dialog.vue'
import creatorCommunity from '@/views/account/creator_community_dialog.vue'
import loginOut from '@/views/account/login_out.vue'
import redemptionCode from '@/views/account/redemption_code.vue'
import redemptionSuccess from '@/views/account/redemption_success.vue'
import removeAccount from '@/views/account/remove_account.vue'
import welfareCenterDialog from '@/views/account/welfare_center.vue'
import { useloginStore } from '@/stores/login'
import avatar from '@/assets/images/account/avatar.png'
import avatarSign from '@/assets/images/account/avatar_sign.png'
import avatarSvipSign from '@/assets/images/account/avatar_svip_sign.svg'
import { showUserBenefits } from '@/api/login.js'
import { useRoute, useRouter } from 'vue-router';
import helpDark from '@/assets/images/account/help_dark.svg'
import helpLight from '@/assets/images/account/help.svg'
import expireImage from '@/assets/images/account/expire.svg'
import { accAdd, accSub, accMul, accDiv } from '@/utils/accuracy'
import { computed } from 'vue';
import { saveDigitalWork, editDigitalWork } from '@/api/digitalHuman.js'
import eventBus from '@/common/utils/eventBus'
import { ElMessage } from "element-plus";
import { checkUploadPermission } from '@/api/upload'; // 导入权限检查接口
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore'; // 导入数字人store
import customerService from "@/views/layout/components/suspension/customer_service.vue"
import buyDigitalDialog from "@/views/account/component/account/buy_digital_dialog.vue"
import buyBusinessDialog from "@/views/account/component/account/buy_business_dialog.vue"
import buyDubDialog from "@/views/account/component/account/buy_dub_dialog.vue"
let rate = 1
let route = useRoute()
let router = useRouter()
let loginStore = useloginStore()

const isDue = ref(false)
const isDueImg = reactive([new URL('@/assets/img/time.png', import.meta.url).href, new URL('@/assets/img/time_gray.png', import.meta.url).href])
// 获取剩余时间
const remainMinutes = computed(() => {
	let endTime = new Date(loginStore.memberInfo.digital_human.end_time),
		endTimeStamp = endTime.getTime(),
		currentTimeStamp = new Date().getTime();
	if (endTimeStamp > currentTimeStamp) {
		// 未过期
		isDue.value = false
		let minutes = Math.floor(parseFloat(loginStore.memberInfo.digital_human.digital_time) / 60),
			seconds = parseFloat(loginStore.memberInfo.digital_human.digital_time) % 60;
		if (minutes == 0) {
			return '剩余' + seconds + '秒'
		} else {
			if (seconds < 10) {
				return '剩余' + minutes + '分钟0' + seconds + '秒'
			} else {
				return '剩余' + minutes + '分钟' + seconds + '秒'
			}
		}
	} else {
		// 已过期
		isDue.value = true
		return '剩余0秒'
	}
})
// 获取数字人store实例，用于获取视频时长
const digitalHumanStore = useDigitalHumanStore()

// 添加获取userId的辅助函数
const getUserId = () => {
	try {
		return JSON.parse(localStorage.getItem('user'))?.userId || '';
	} catch (error) {
		console.error('获取userId失败:', error);
		return '';
	}
};

// 检测是否为数字人页面
const isDigitalHumanPage = computed(() => {
	return route.path === '/digital-human-transition' || route.name === 'DigitalHumanTransition' ||
	       route.path === '/digital-human-editor-page' || route.name === 'DigitalHumanEditorPage'
})

// 检测是否为编辑模式（通过route.query.id判断）
const isEditMode = computed(() => {
	return route.query.id && route.query.id.toString().trim() !== ''
})


let user = reactive({
	avatar: avatar,
	avatarSign: avatarSign,
	avatarSvipSign
})
// 登录按钮方法
const { proxy } = getCurrentInstance();
let isTooltipVisible = ref(true);
let popperOptions = ref({
	modifiers: [
		{
			name: 'preventNegativeY',
			enabled: true,
			phase: 'main',
			fn({ state }) {
				if (state.modifiersData.popperOffsets) {
					if (state.modifiersData.popperOffsets.y < 76) {
						if (rate < 1) {
							state.modifiersData.popperOffsets.y = state.modifiersData.popperOffsets.y / rate
						} else {
							state.modifiersData.popperOffsets.y = 76
						}
					}
				}
			}
		},
		{
			name: 'preventOverflow',
			options: {
				boundary: 'viewport'
			}
		},
		{
			name: 'offset',
			options: {
				offset: [-170, 27], // [x, y] 偏移量，x 为水平偏移，y 为垂直偏移
			},
		},
	],
});
const loginButton = () => {
	// proxy.$router.push({ name: 'login' })
	console.log(proxy.$modal)
	// proxy.$showLoginModal();
	proxy.$modal.open('组合式标题')
}
let account_info_ref = ref(null)
let login_out_ref = ref(null)
let redemption_code_ref = ref(null)
let redemption_success_ref = ref(null)
let accountInfoClick = () => {


	account_info_ref.value.dialogVisable = true
}
let creator_community_ref = ref(null)
let creator_community = () => {
	creator_community_ref.value.dialogVisable = true
}
let loginout = () => {
	login_out_ref.value.dialogVisible = true
}
let redemption_code = () => {
	redemption_code_ref.value.dialogVisible = true
}
let redemption_success = (data) => {
	redemption_success_ref.value.dialogVisible = true
	redemption_success_ref.value.info = data
}
let use_help = () => {
	window.open('https://pcnrmu4lahrm.feishu.cn/wiki/RevuwiVIrifw3tkNV7Vc0xganFb?from=from_copylink', '_blank')

}
let go_help = () => {
	window.open('https://pcnrmu4lahrm.feishu.cn/wiki/RevuwiVIrifw3tkNV7Vc0xganFb', '_blank')
}
let sumSurplus = (a = 0, b = 0) => {
	return accSub(a, b)
}
let getComputing = (data) => {
	let current = ''


	if (data == 'calculate') {
		current = sumSurplus(((loginStore.memberInfo && loginStore.memberInfo.total && loginStore.memberInfo.total['One-Click Video']) || 0), ((loginStore.memberInfo && loginStore.memberInfo.current && loginStore.memberInfo.current['One-Click Video']) || 0))

	} else if (data == 'listening') {
		current = sumSurplus(((loginStore.memberInfo && loginStore.memberInfo.total && loginStore.memberInfo.total['free_resource']) || 0), ((loginStore.memberInfo && loginStore.memberInfo.current && loginStore.memberInfo.current['free_resource']) || 0))
	} else {
		current = sumSurplus(((loginStore.memberInfo && loginStore.memberInfo.total && loginStore.memberInfo.total['SFT']) || 0), ((loginStore.memberInfo && loginStore.memberInfo.current && loginStore.memberInfo.current['SFT']) || 0))
	}

	// let total=(loginStore.memberInfo&&loginStore.memberInfo.total&&loginStore.memberInfo.total['One-Click Video'])|| 0
	return current
}
let expire = ref(false)
let signImg = ref('')

watch(
	() => [loginStore?.memberInfo?.level?.level, expire.value],
	([benefitLevel, expireVal]) => {
		switch (benefitLevel) {
			case 1:
				signImg.value = avatarSign
				break
			case 2:
				signImg.value = avatarSvipSign
				break
			default:
				signImg.value = ''
		}
		if (expire.value) {
			signImg.value = expireImage
		}
	},
	{ immediate: true }
)

let isExpired = (expireTime) => {
	const expireISO = expireTime.replace(' ', 'T');
	const expireDate = new Date(expireISO);
	const now = new Date();

	if (isNaN(expireDate.getTime())) {
		console.warn('无效的时间格式:', expireTime);
		return false;
	}
	return now.getTime() > expireDate.getTime();
}
let remove_account_ref = ref(null)
let remove_account = () => {
	remove_account_ref.value.dialogVisible = true
}
let welfare_center_ref = ref(null)
const requireLogin = (callback) => {
	if (!loginStore.token) {
		proxy.$modal.open('组合式标题');
	} else {
		callback();
	}
};
let welfare_center = () => {
	requireLogin(() => {
		welfare_center_ref.value.dialogVisible = true
	});
}
let getBodyScale = () => {
	const el = document.querySelector('#app');
	if (!el) return 1;
	const transform = getComputedStyle(el).transform;
	if (!transform || transform === 'none') return 1;
	const values = transform.match(/matrix\((.+)\)/)[1].split(', ');
	return parseFloat(values[0]); // scale
}
onMounted(async () => {
	if (loginStore.userId && loginStore.userId != ''&&loginStore.token&&loginStore.token != '') {
		let res = await showUserBenefits({ userId: loginStore.userId })
		if (res.code != 0) {
			// ElMessage.error(res.msg) 
			return
		}
		let member_data = res?.data?.content
		member_data.result && loginStore.setMemberInfo(member_data.result)

	}

	if (loginStore.memberInfo && loginStore.memberInfo.level && loginStore.memberInfo.level.end_time) {
		expire.value = isExpired(loginStore.memberInfo.level.end_time || '')
	}
	rate = getBodyScale()
})

let showGenerateVideoDialog = ref(false);
let isGeneratingVideo = ref(false); // 生成视频的加载状态

const handleGenerateVideoClick = () => {
	// 检查用户是否已经同意过
	const hasAgreed = localStorage.getItem('userStatementAgreed');
	if (hasAgreed === 'true') {
		// 如果已同意，直接执行生成逻辑，不显示弹窗
		console.log("User has already agreed. Proceeding to video generation.");
		// 执行保存数字人作品
		handleSaveDigitalWork();
	} else {
		// 如果未同意，则显示声明弹窗
		showGenerateVideoDialog.value = true;
	}
};

const closeConfirmationDialog = () => {
	showGenerateVideoDialog.value = false;
};

const confirmGeneration = () => {
	// 记录用户已同意
	localStorage.setItem('userStatementAgreed', 'true');
	console.log("Video generation confirmed and agreement stored.");
	closeConfirmationDialog();
	// 执行保存数字人作品
	handleSaveDigitalWork();
};

/**
 * 构建保存数字人作品的参数
 * 根据编辑器数据构建符合API要求的参数结构
 */
const buildSaveParams = (editorData) => {
	try {
		// 基础信息 - 从编辑器标题获取，如果没有则使用默认值
		const title = editorData?.title || "未命名作品";
		const userId = loginStore.userId || '';

		// 从编辑器数据中提取右侧面板数据
		const rightPanelData = editorData?.rightPanelData?.data || {};
		const digitalHumanRightOption = rightPanelData.digital_human_right_option || {};

		// 构建 audioJson - 根据右侧面板的真实数据和模式类型
		const captionsData = rightPanelData.captions || {};
		const chooseDubData = rightPanelData.choose_dub || {};

		// 获取模式类型：text_captions(输入文本) 或 aduio_captions(音频驱动)
		const modeType = digitalHumanRightOption.type || 'text_captions';

		// 🎬 获取字幕JSON数组 - 从右侧操作面板传递的数据中提取
		const subtitleJsonArray = digitalHumanRightOption.subtitle_json ||
			digitalHumanRightOption.audioJson?.subtitle_json ||
			digitalHumanRightOption.aduio_data?.subtitle_json ||
			digitalHumanRightOption.aduio_data?.subtitle_data ||  // 🔧 修复：音频驱动模式的正确路径
			digitalHumanRightOption.subtitle_data_with_time ||     // 🔧 修复：备选路径
			[];

		console.log('🎬 字幕JSON数组获取情况:', {
			模式类型: modeType,
			'digitalHumanRightOption.subtitle_json': digitalHumanRightOption.subtitle_json,
			'digitalHumanRightOption.audioJson?.subtitle_json': digitalHumanRightOption.audioJson?.subtitle_json,
			'digitalHumanRightOption.aduio_data?.subtitle_json': digitalHumanRightOption.aduio_data?.subtitle_json,
			'🔧 digitalHumanRightOption.aduio_data?.subtitle_data': digitalHumanRightOption.aduio_data?.subtitle_data,
			'🔧 digitalHumanRightOption.subtitle_data_with_time': digitalHumanRightOption.subtitle_data_with_time,
			最终获取的字幕数组: subtitleJsonArray,
			字幕数组长度: subtitleJsonArray.length,
			字幕数组样例: subtitleJsonArray[0] || '无数据'
		});

		/**
		 * 🎵 音频驱动模式数据源说明：
		 *   - audioJson: digitalHumanRightOption.audioJson (包含音频URL、名称、音量等)
		 *   - 提取文本: digitalHumanRightOption.aduio_data.textarea (接口返回的音频识别文本)
		 *   - 字幕开关: digitalHumanRightOption.open_captions
		 * 
		 * 📝 输入文本模式数据源说明：
		 *   - 保持原有逻辑不变，从各自面板获取数据
		 *   - 字幕开关: rightPanelData.captions.open_captions
		 */

		console.log('🔍 右侧面板模式判断:', {
			模式类型: modeType,
			数字人右侧选项完整数据: digitalHumanRightOption,
			音频JSON数据: digitalHumanRightOption.audioJson,
			音频上传数据: digitalHumanRightOption.aduio_data,
			提取的文本: digitalHumanRightOption.aduio_data?.textarea,
			字幕开关: digitalHumanRightOption.open_captions,
			背景音乐: digitalHumanRightOption.choose_music
		});

		// 🔍 额外调试：检查audioJson中的wav_url字段
		console.log('🔍 详细检查audioJson数据:', {
			'digitalHumanRightOption.audioJson': digitalHumanRightOption.audioJson,
			'audioJson.wav_url': digitalHumanRightOption.audioJson?.wav_url,
			'audioJson.wav_name': digitalHumanRightOption.audioJson?.wav_name,
			'audioJson.duration': digitalHumanRightOption.audioJson?.duration,
			'aduio_data.original_audio_url': digitalHumanRightOption.aduio_data?.original_audio_url,
			'aduio_data完整数据': digitalHumanRightOption.aduio_data
		});

		let audioJson = {};

		// 🔄 编辑模式下优先使用原始audioJson数据
		const originalWorkData = editorData?.originalWorkData;
		if (originalWorkData && originalWorkData.audioJson && isEditMode.value) {
			console.log('🔄 编辑模式：使用原始audioJson数据', {
				原始audioJson: originalWorkData.audioJson,
				包含字段: originalWorkData.audioJson ? Object.keys(originalWorkData.audioJson) : null,
				wav_url: originalWorkData.audioJson.wav_url,
				wav_text: originalWorkData.audioJson.wav_text,
				type: originalWorkData.audioJson.type
			});

			// 直接使用原始的audioJson，确保所有字段完整传递
			audioJson = { ...originalWorkData.audioJson };
		} else {
			console.log('🆕 新建模式或无原始数据：重新构建audioJson');
		}

		// 🔄 只有在新建模式或编辑模式无原始数据时才重新构建audioJson
		if (!originalWorkData || !originalWorkData.audioJson || !isEditMode.value) {
			if (modeType === 'aduio_captions') {
				// 音频驱动模式 - 使用实际的数据结构
				const audioJsonData = digitalHumanRightOption.audioJson || {}; // 音频JSON数据
				const audioUploadData = digitalHumanRightOption.aduio_data || {};
				const audioFileData = audioUploadData.aduio || {}; // aduio_data.aduio 对象
				const extractedText = audioUploadData.textarea || ""; // 接口返回的提取文本

				// 音频URL优先级：aduio_data.aduio.url > audioJson.wav_url
				const audioUrl = audioFileData.url || audioJsonData.wav_url || "";
				const audioName = audioFileData.name || audioJsonData.wav_name || "";
				const audioVolume = audioFileData.volume !== undefined ? audioFileData.volume : audioJsonData.volume;
				const audioDuration = audioFileData.duration || audioJsonData.duration || 0;

				console.log('🔍 音频驱动数据解析:', {
					音频JSON数据: audioJsonData,
					音频上传数据: audioUploadData,
					音频文件数据: audioFileData,
					'aduio_data.aduio.url': audioFileData.url,
					'audioJson.wav_url': audioJsonData.wav_url,
					最终音频URL: audioUrl,
					最终音频名称: audioName,
					最终音频音量: audioVolume,
					音频时长: audioDuration,
					提取的文本: extractedText,
					字幕文本: audioJsonData.wav_text
				});

				audioJson = {
					type: "audio", // 🔧 修复：统一使用"audio"类型
					tts: {
						text: extractedText ? [extractedText] : (audioJsonData.wav_text ? [audioJsonData.wav_text] : [""]), // 优先使用提取的文本
						speed: parseFloat(chooseDubData.speech || 1.0),
						audio_man: "",
						pitch: parseFloat(chooseDubData.intonation || 100)
					},
					wav_url: audioUrl, // 优先使用 aduio_data.aduio.url
					wav_name: audioName, // 优先使用 aduio_data.aduio.name
					wav_text: extractedText || audioJsonData.wav_text || "", // 优先使用提取的文本作为字幕
					volume: Math.round(audioVolume || 100), // 使用音频文件设置的音量
					duration: audioDuration, // 音频时长
					language: audioJsonData.language || "cn",
					voiceId: "audio_drive", // 标识为音频驱动
					voicePerson: "音频驱动",
					voiceImg: ""
				};
			} else {
				// 输入文本模式 - 使用digitalHumanRightOption中的音频数据
				const audioJsonData = digitalHumanRightOption.audioJson || {}; // 从右侧操作面板获取音频JSON数据
				const audioUploadData = digitalHumanRightOption.aduio_data || {}; // TTS生成的音频数据

				// 🔧 修复：接口调用优先使用original_audio_url字段
				const audioUrl = audioUploadData.original_audio_url || digitalHumanRightOption.interfaceAudioUrl || audioJsonData.wav_url || "";
				const audioName = audioJsonData.wav_name || audioUploadData.audio_name || "";
				const audioDuration = audioJsonData.duration || audioUploadData.extra_info?.audio_length || 0;

				console.log('🎵 接口调用音频URL获取:', {
					'original_audio_url': audioUploadData.original_audio_url,
					'interfaceAudioUrl': digitalHumanRightOption.interfaceAudioUrl,
					'wav_url': audioJsonData.wav_url,
					'最终使用': audioUrl
				});

				console.log('🔍 输入文本模式数据解析:', {
					音频JSON数据: audioJsonData,
					音频上传数据: audioUploadData,
					'audioJson.wav_url': audioJsonData.wav_url,
					'aduio_data.original_audio_url': audioUploadData.original_audio_url,
					最终音频URL: audioUrl,
					最终音频名称: audioName,
					音频时长: audioDuration,
					配音数据: chooseDubData
				});

				audioJson = {
					type: "audio", // 🔧 修复：统一使用"audio"类型
					tts: {
						text: captionsData.textInfo ? [captionsData.textInfo] : ["欢迎使用蝉镜数字人视频合成服务！"],
						speed: parseFloat(chooseDubData.speech || 1.0),
						audio_man: "",
						pitch: parseFloat(chooseDubData.intonation || 100)
					},
					// 🔧 修复：使用正确的音频URL来源
					wav_url: audioUrl,
					wav_name: audioName,
					wav_text: captionsData.textInfo || "",
					volume: Math.round(chooseDubData.volume || 100),
					duration: audioDuration, // 添加音频时长
					language: "cn",
					voiceId: chooseDubData.current_character?.info?.id || 1,
					voicePerson: chooseDubData.current_character?.info?.voiceName || "默认音色",
					voiceImg: chooseDubData.current_character?.info?.voiceImg || ""
				};
			}
		} // 🔄 结束audioJson重构的条件判断

		// 构建 personJson - 从左侧面板和PreviewEditor实时位置数据获取数字人数据
		const leftPanelData = editorData?.leftPanelData || {};

		// 🎯 获取PreviewEditor组件的实时位置数据（页面坐标系）
		const positionsData = editorData?.positionsData || {};

		// 🎯 获取PreviewEditor组件的标准坐标数据（动态标准坐标系，供接口使用）
		const standardPositionsData = editorData?.standardPositionsData || {};

		// 🎯 获取宽高比，用于动态设置标准坐标系尺寸
		const aspectRatio = editorData?.aspectRatio || '9:16';

		console.log('🔍 左侧面板数据:', leftPanelData);
		console.log('🔍 PreviewEditor实时位置数据（页面坐标）:', positionsData);
		console.log('🔍 PreviewEditor标准坐标数据（动态标准坐标系）:', standardPositionsData);
		console.log('🔍 数字人ID来源:', {
			从左侧面板: leftPanelData.digitalHuman?.id,
			从配置: editorData?.digitalHumanConfig?.id,
			默认值: "8217cb18710849d7acbf2c6da9d002e9"
		});
		console.log('🔍 坐标数据对比:', {
			页面坐标_数字人: {
				x: positionsData.character?.x,
				y: positionsData.character?.y,
				width: positionsData.character?.width,
				height: positionsData.character?.height
			},
			标准坐标_数字人: {
				x: standardPositionsData.character?.x,
				y: standardPositionsData.character?.y,
				width: standardPositionsData.character?.width,
				height: standardPositionsData.character?.height
			}
		});

		// 🔍 关键调试：检查数据是否为空
		console.log('🚨 关键数据检查:', {
			positionsData是否存在: !!positionsData,
			'positionsData.character是否存在': !!positionsData?.character,
			standardPositionsData是否存在: !!standardPositionsData,
			'standardPositionsData.character是否存在': !!standardPositionsData?.character,
			editorData是否存在: !!editorData
		});

		// 🔧 坐标验证和标准化函数（支持边界限制）
		const validateCoordinate = (value, min = -Infinity, max = Infinity) => {
			if (typeof value === 'number' && !isNaN(value)) {
				// 先限制在边界范围内，再四舍五入
				const clampedValue = Math.max(min, Math.min(max, value));
				return Math.round(clampedValue);
			}
			// 如果值无效，返回0（而不是边界值）
			return 0;
		};

		// 🎯 确保数值为偶数的函数（视频编码要求）
		const makeEven = (value) => {
			const rounded = Math.round(value);
			return rounded % 2 === 0 ? rounded : rounded + 1;
		};

		// 获取数字人的实际尺寸（用户可能已经拉伸调整过）
		const digitalHumanWidth = positionsData.character?.width || 380;  // 默认宽度
		const digitalHumanHeight = positionsData.character?.height || 700; // 默认高度

		// 获取数字人类型 - 直接从配置中获取figures_type
		const figureType = editorData?.digitalHumanConfig?.figures_type || "whole_body";

		// 🔧 使用标准坐标数据构建personJson（动态标准坐标系）
		const standardCharacterData = standardPositionsData.character || {};

		// 🔍 关键调试：追踪标准坐标数据来源
		console.log('🔍 标准坐标数据来源追踪:', {
			standardCharacterData: standardCharacterData,
			原始X: standardCharacterData.x,
			原始Y: standardCharacterData.y,
			X类型: typeof standardCharacterData.x,
			Y类型: typeof standardCharacterData.y
		});

		// 🚨 修复：允许负坐标，直接使用转换后的标准坐标
		// 标准坐标系允许负坐标（元素可以部分超出画布边界）
		const standardSize = aspectRatio === '16:9' ? { width: 1920, height: 1080 } : { width: 1080, height: 1920 };

		// 🔧 直接使用标准坐标，只进行数值验证，不限制边界
		const characterX = validateCoordinate(standardCharacterData.x);
		const characterY = validateCoordinate(standardCharacterData.y);
		const characterWidth = makeEven(validateCoordinate(standardCharacterData.width, 50, standardSize.width * 2));
		const characterHeight = makeEven(validateCoordinate(standardCharacterData.height, 50, standardSize.height * 2));

		// 🔍 最终personJson构建调试
		console.log('🔍 最终personJson构建调试:', {
			输入标准坐标: {
				x: standardCharacterData.x,
				y: standardCharacterData.y,
				width: standardCharacterData.width,
				height: standardCharacterData.height
			},
			处理后坐标: {
				x: characterX,
				y: characterY,
				width: characterWidth,
				height: characterHeight
			},
			坐标变化: {
				X轴: `${standardCharacterData.x} → ${characterX}`,
				Y轴: `${standardCharacterData.y} → ${characterY}`,
				宽度: `${standardCharacterData.width} → ${characterWidth}`,
				高度: `${standardCharacterData.height} → ${characterHeight}`
			}
		});

		const personJson = {
			id: leftPanelData.digitalHuman?.id || editorData?.digitalHumanConfig?.id || "8217cb18710849d7acbf2c6da9d002e9",
			x: characterX,              // 🔧 修复：使用标准坐标系的X坐标
			y: characterY,              // 🔧 修复：使用标准坐标系的Y坐标
			width: characterWidth,      // 🔧 修复：使用标准坐标系的宽度（用户实际调整后的尺寸）
			height: characterHeight,    // 🔧 修复：使用标准坐标系的高度（用户实际调整后的尺寸）
			figure_type: figureType,    // 直接使用获取到的figures_type
			drive_mode: "",
			is_rgba_mode: false,
			backway: 1
		};

		// 🔍 关键调试：坐标转换链路追踪
		console.log('🔍 坐标转换链路:', {
			标准坐标: { x: standardCharacterData.x, y: standardCharacterData.y },
			最终结果: { x: characterX, y: characterY }
		});

		// 🔧 数字人坐标调试日志
		const originalWidth = validateCoordinate(standardCharacterData.width, digitalHumanWidth);
		const originalHeight = validateCoordinate(standardCharacterData.height, digitalHumanHeight);
		console.log('🧑‍🎨 数字人层坐标验证（标准坐标系）:', {
			页面坐标: {
				x: positionsData.character?.x,
				y: positionsData.character?.y,
				width: positionsData.character?.width,
				height: positionsData.character?.height
			},
			标准坐标: {
				x: characterX,
				y: characterY,
				width: characterWidth,
				height: characterHeight
			},
			偶数处理: {
				原始宽度: originalWidth,
				偶数宽度: characterWidth,
				原始高度: originalHeight,
				偶数高度: characterHeight,
				宽度调整: originalWidth !== characterWidth ? `${originalWidth} → ${characterWidth}` : '无需调整',
				高度调整: originalHeight !== characterHeight ? `${originalHeight} → ${characterHeight}` : '无需调整'
			},
			宽高比: aspectRatio,
			标准尺寸: `${characterWidth}×${characterHeight}`,
			位置来源: standardPositionsData.character ? 'PreviewEditor标准坐标数据' : '默认值',
			尺寸来源: standardPositionsData.character ? 'PreviewEditor标准坐标数据（用户调整后，偶数处理）' : '默认值（偶数处理）'
		});

		// 构建 bgJson - 从左侧面板和PreviewEditor实时位置数据获取背景数据
		// 🔧 修复：根据backgroundConfig.type正确区分背景色和背景图片
		let backgroundImageUrl = "";
		const backgroundConfig = editorData?.backgroundConfig;

		// 优先使用backgroundConfig判断背景类型
		if (backgroundConfig && backgroundConfig.type === 'image') {
			// 背景图片模式：使用backgroundConfig中的图片URL
			backgroundImageUrl = backgroundConfig.value || "";
		} else if (backgroundConfig && backgroundConfig.type === 'color') {
			// 背景色模式：src_url应该为空
			backgroundImageUrl = "";
		} else {
			// 回退逻辑：检查左侧面板数据（向后兼容）
			const backgroundData = leftPanelData.background;
			if (backgroundData && typeof backgroundData === 'object') {
				// 优先获取OSS路径（图案背景）
				backgroundImageUrl = backgroundData.ossPath || backgroundData.storagePath || "";
			}
		}

		/**
		 * 获取预览区域的真实尺寸作为默认值
		 * 替换硬编码的1920×1080，使用与PreviewEditor组件一致的尺寸
		 */
		const getPreviewAreaSize = (aspectRatio) => {
			if (aspectRatio === '16:9') {
				return { width: 901.333, height: 507 };
			} else if (aspectRatio === '9:16') {
				return { width: 403, height: 700 };
			} else {
				return { width: 492, height: 749 };
			}
		};

		// 🔧 简化背景尺寸获取逻辑：两个优先级
		let backgroundWidth, backgroundHeight;

		// 优先级1：使用用户拖拽调整后的尺寸（如果存在）
		if (positionsData.backgroundModule?.width && positionsData.backgroundModule?.height) {
			backgroundWidth = positionsData.backgroundModule.width;
			backgroundHeight = positionsData.backgroundModule.height;

			console.log('🎯 使用用户调整的背景尺寸（优先级1）:', {
				背景URL: backgroundImageUrl,
				用户调整宽度: backgroundWidth,
				用户调整高度: backgroundHeight,
				说明: '用户已拖拽调整背景尺寸'
			});
		}
		// 优先级2：使用预览区域尺寸
		else {
			const previewSize = getPreviewAreaSize(aspectRatio);
			backgroundWidth = previewSize.width;
			backgroundHeight = previewSize.height;

			console.log('📐 使用预览区域尺寸（优先级2）:', {
				背景URL: backgroundImageUrl,
				长宽比: aspectRatio,
				预览区域宽度: backgroundWidth,
				预览区域高度: backgroundHeight,
				说明: '使用预览区域默认尺寸'
			});
		}

		// 🔧 新增：背景数据处理调试日志
		console.log('🔍 背景配置分析:', {
			backgroundConfig: backgroundConfig,
			配置类型: backgroundConfig?.type,
			配置值: backgroundConfig?.value,
			最终背景URL: backgroundImageUrl,
			是否为背景色模式: backgroundConfig?.type === 'color',
			是否为背景图片模式: backgroundConfig?.type === 'image'
		});

		console.log('🔍 最终背景数据汇总 (第一层背景):', {
			背景URL: backgroundImageUrl,
			x坐标: positionsData.backgroundModule?.x || 0,
			y坐标: positionsData.backgroundModule?.y || 0,
			最终宽度: backgroundWidth,
			最终高度: backgroundHeight,
			数据来源: positionsData.backgroundModule?.width ? '用户调整' : (backgroundImageUrl ? '图片真实尺寸' : '默认值')
		});

		// 🔧 使用标准坐标数据构建bgJson（1920×1080标准坐标系）
		const standardBackgroundData = standardPositionsData.backgroundModule || {};
		const backgroundX = validateCoordinate(standardBackgroundData.x, 0);
		const backgroundY = validateCoordinate(standardBackgroundData.y, 0);
		const standardBackgroundWidth = makeEven(validateCoordinate(standardBackgroundData.width, backgroundWidth));
		const standardBackgroundHeight = makeEven(validateCoordinate(standardBackgroundData.height, backgroundHeight));

		const bgJson = {
			src_url: backgroundImageUrl,
			x: backgroundX,                    // 🔧 修复：使用标准坐标系的X坐标
			y: backgroundY,                    // 🔧 修复：使用标准坐标系的Y坐标
			width: standardBackgroundWidth,    // 🔧 修复：使用标准坐标系的宽度
			height: standardBackgroundHeight   // 🔧 修复：使用标准坐标系的高度
		};

		// 🔧 背景层坐标调试日志
		const originalBgWidth = validateCoordinate(standardBackgroundData.width, backgroundWidth);
		const originalBgHeight = validateCoordinate(standardBackgroundData.height, backgroundHeight);
		console.log('🏞️ 背景层坐标验证（标准坐标系）:', {
			页面坐标: {
				x: positionsData.backgroundModule?.x,
				y: positionsData.backgroundModule?.y,
				width: positionsData.backgroundModule?.width,
				height: positionsData.backgroundModule?.height
			},
			标准坐标: {
				x: backgroundX,
				y: backgroundY,
				width: standardBackgroundWidth,
				height: standardBackgroundHeight
			},
			偶数处理: {
				原始宽度: originalBgWidth,
				偶数宽度: standardBackgroundWidth,
				原始高度: originalBgHeight,
				偶数高度: standardBackgroundHeight,
				宽度调整: originalBgWidth !== standardBackgroundWidth ? `${originalBgWidth} → ${standardBackgroundWidth}` : '无需调整',
				高度调整: originalBgHeight !== standardBackgroundHeight ? `${originalBgHeight} → ${standardBackgroundHeight}` : '无需调整'
			},
			坐标来源: standardPositionsData.backgroundModule ? 'PreviewEditor标准坐标数据' : '默认值',
			坐标系: '动态标准坐标系（偶数处理）'
		});

		// 构建 subtitleConfigJson - 字幕配置
		// 根据模式类型区分获取字幕开关状态
		let subtitleShow;
		if (modeType === 'aduio_captions') {
			// 音频驱动模式 - 从 digital_human_right_option.open_captions 获取
			subtitleShow = digitalHumanRightOption.open_captions !== undefined ?
				digitalHumanRightOption.open_captions :
				(editorData?.subtitleVisible !== false); // 默认显示字幕
		} else {
			// 输入文本模式 - 从 rightPanelData.captions.open_captions 获取
			subtitleShow = rightPanelData.captions?.open_captions !== undefined ?
				rightPanelData.captions.open_captions :
				(editorData?.subtitleVisible !== false); // 默认显示字幕
		}

		console.log('🔍 字幕开关数据来源:', {
			模式类型: modeType,
			音频驱动字幕开关: digitalHumanRightOption.open_captions,
			输入文本字幕开关: rightPanelData.captions?.open_captions,
			配置中的可见性: editorData?.subtitleVisible,
			最终使用: subtitleShow
		});

		// 🔧 获取字幕层的标准坐标尺寸（已经正确转换过的）
		const subtitleWidth = standardPositionsData.subtitle?.width || (aspectRatio === '16:9' ? 960 : 378);
		const subtitleHeight = standardPositionsData.subtitle?.height || (aspectRatio === '16:9' ? 108 : 80);

		console.log('🔍 字幕位置数据来源 (标准坐标系):', {
			页面坐标: {
				x: positionsData.subtitle?.x,
				y: positionsData.subtitle?.y,
				width: positionsData.subtitle?.width,
				height: positionsData.subtitle?.height
			},
			标准坐标: {
				x: standardPositionsData.subtitle?.x,
				y: standardPositionsData.subtitle?.y,
				width: subtitleWidth,
				height: subtitleHeight
			}
		});

		console.log('🔍 字幕样式数据来源 (左侧面板):', {
			字体大小: editorData?.subtitleConfig?.fontSize,
			字体颜色: editorData?.subtitleConfig?.textColor,
			描边颜色: editorData?.subtitleConfig?.borderColor,
			描边宽度: editorData?.subtitleConfig?.borderWidth,
			字体ID: editorData?.subtitleConfig?.fontFamily
		});

		// 🔧 使用标准坐标数据构建subtitleConfigJson（1920×1080标准坐标系）
		const standardSubtitleData = standardPositionsData.subtitle || {};
		const subtitleX = validateCoordinate(standardSubtitleData.x, 0);
		const subtitleY = validateCoordinate(standardSubtitleData.y, 0);

		// 🔧 字幕尺寸和字体大小处理（页面坐标缩放到标准坐标）
		const originalPageSize = standardPositionsData.previewWindow?.originalPageSize;
		let scaledFontSize = editorData?.subtitleConfig?.fontSize || 18;

		// 🎯 字体大小缩放：从页面坐标缩放到标准坐标
		if (originalPageSize && originalPageSize.width > 0 && originalPageSize.height > 0) {
			// 🔧 获取动态标准尺寸（需要在screenWidth/screenHeight定义之后）
			// 先获取宽高比对应的标准高度
			const standardHeight = aspectRatio === '16:9' ? 1080 : 1920;

			// 计算缩放比例（页面到标准坐标）
			const scaleY = standardHeight / originalPageSize.height;

			// 字体大小按Y轴比例缩放
			scaledFontSize = Math.round(scaledFontSize * scaleY);

			console.log('🔤 字体大小缩放处理:', {
				原始页面字体大小: editorData?.subtitleConfig?.fontSize || 18,
				页面高度: originalPageSize.height,
				标准高度: standardHeight,
				Y轴缩放比例: scaleY.toFixed(4),
				缩放后字体大小: scaledFontSize,
				宽高比: aspectRatio
			});
		}

		const scaledSubtitleWidth = makeEven(subtitleWidth);
		const scaledSubtitleHeight = makeEven(subtitleHeight);

		console.log('🎯 字幕最终配置数据:', {
			字体大小: scaledFontSize,
			字幕宽度: scaledSubtitleWidth,
			字幕高度: scaledSubtitleHeight,
			宽高比: aspectRatio,
			说明: '直接使用标准坐标系数据，无需额外缩放转换'
		});

		const subtitleConfigJson = {
			show: subtitleShow, // 使用右侧面板的字幕开关状态
			x: subtitleX,                    // 🔧 修复：使用标准坐标系的X坐标
			y: subtitleY,                    // 🔧 修复：使用标准坐标系的Y坐标
			width: scaledSubtitleWidth,      // 🎯 修改：使用缩放后的字幕宽度
			height: scaledSubtitleHeight,    // 🎯 修改：使用缩放后的字幕高度
			font_size: scaledFontSize,                                          // 🎯 修改：使用缩放后的字体大小
			color: editorData?.subtitleConfig?.textColor || "#FFFFFF",          // 从左侧获取字体颜色
			stroke_color: (editorData?.subtitleConfig?.borderColor === '#000000') ? "" : (editorData?.subtitleConfig?.borderColor || ""),        // 🔧 修复：过滤旧默认值
			stroke_width: (editorData?.subtitleConfig?.borderWidth === 7 || editorData?.subtitleConfig?.borderWidth === 12) ? 0 : (editorData?.subtitleConfig?.borderWidth || 0),         // 🔧 修复：过滤旧默认值
			font_id: editorData?.subtitleConfig?.fontFamily || "font_001"       // 从左侧获取字体样式ID
		};

		// 🔧 字幕层坐标调试日志
		console.log('📝 字幕层坐标验证（标准坐标系）:', {
			页面坐标: {
				x: positionsData.subtitle?.x,
				y: positionsData.subtitle?.y,
				width: positionsData.subtitle?.width,
				height: positionsData.subtitle?.height
			},
			标准坐标: {
				x: subtitleX,
				y: subtitleY,
				width: scaledSubtitleWidth,
				height: scaledSubtitleHeight
			},
			字体配置: {
				原始页面字体大小: editorData?.subtitleConfig?.fontSize || 18,
				缩放后标准字体大小: scaledFontSize,
				最终字体大小: scaledFontSize,
				字体颜色: editorData?.subtitleConfig?.textColor,
				描边: editorData?.subtitleConfig?.borderWidth > 0
			},
			显示状态: subtitleShow,
			宽高比: aspectRatio,
			坐标来源: 'PreviewEditor标准坐标系（直接使用）',
			坐标系: aspectRatio === '16:9' ? '1920×1080标准坐标系' : '1080×1920标准坐标系'
		});

		// 🔧 修复：优先从backgroundConfig获取背景色
		let bgColor = "#F5F5F5"; // 默认背景色

		if (backgroundConfig && backgroundConfig.type === 'color') {
			// 背景色模式：使用backgroundConfig中的颜色值
			bgColor = backgroundConfig.value || "#F5F5F5";
		} else {
			// 回退逻辑：从左侧面板数据获取（向后兼容）
			bgColor = leftPanelData.background?.color ||
				leftPanelData.backgroundColor ||
				"#F5F5F5";
		}

		// 🔧 新增：背景色获取调试日志
		console.log('🎨 背景色获取分析:', {
			backgroundConfig: backgroundConfig,
			配置类型: backgroundConfig?.type,
			配置值: backgroundConfig?.value,
			最终背景色: bgColor,
			获取来源: backgroundConfig?.type === 'color' ? 'backgroundConfig' : 'leftPanelData回退',
			leftPanelData背景: leftPanelData.background
		});

		// 🔧 修复：根据宽高比动态设置标准屏幕尺寸（与PreviewEditor标准坐标系保持一致）
		let screenWidth, screenHeight;

		if (aspectRatio === '16:9') {
			screenWidth = 1920;   // 16:9横屏：标准宽度1920
			screenHeight = 1080;  // 16:9横屏：标准高度1080
		} else {
			screenWidth = 1080;   // 9:16竖屏：标准宽度1080  
			screenHeight = 1920;  // 9:16竖屏：标准高度1920
		}

		console.log('📺 屏幕尺寸设置:', {
			aspectRatio,
			screenWidth,
			screenHeight,
			说明: `根据宽高比${aspectRatio}动态设置标准尺寸，与PreviewEditor标准坐标系保持一致`
		});

		// 🔧 修复：统一音频URL的获取逻辑，接口调用优先使用original_audio_url
		const audioJsonData = digitalHumanRightOption.audioJson || {}; // 从右侧操作面板获取音频JSON数据
		const audioUploadData = digitalHumanRightOption.aduio_data || {}; // TTS生成的音频数据

		// 🎵 接口调用优先使用original_audio_url字段
		const audioUrl = audioUploadData.original_audio_url || digitalHumanRightOption.interfaceAudioUrl || audioJsonData.wav_url || "";

		// 🔧 修复：videoUrl 应该与接口传参的音频URL保持一致
		const videoUrl = audioUrl ||  // 优先使用接口传参的音频URL
			rightPanelData.synthesis_url ||
			rightPanelData.audio_drive_url ||
			digitalHumanRightOption.synthesis_url ||
			digitalHumanRightOption.audio_drive_url ||
			"";

		console.log('🎵 videoUrl获取逻辑:', {
			'original_audio_url': audioUploadData.original_audio_url,
			'interfaceAudioUrl': digitalHumanRightOption.interfaceAudioUrl,
			'wav_url': audioJsonData.wav_url,
			'最终videoUrl': videoUrl
		});

		// 获取背景音乐url（从digital_human_right_option.choose_music中）
		const bgmiAudioUrl = digitalHumanRightOption.choose_music?.bgm_url ||
			digitalHumanRightOption.choose_music?.url ||
			digitalHumanRightOption.choose_music?.audio_url ||
			digitalHumanRightOption.choose_music?.music_url ||
			"";

		console.log('🔍 零散参数数据来源:', {
			标题: { 来源: 'editorData.title', 值: title },
			用户ID: { 来源: 'loginStore.userId', 值: userId },
			背景色: { 来源: 'leftPanelData.background.color', 值: bgColor },
			屏幕宽度: { 来源: 'window.screen.width', 值: screenWidth },
			屏幕高度: { 来源: 'window.screen.height', 值: screenHeight },
			视频URL: { 来源: 'audioJson.wav_url优先，回退到synthesis/audio_drive', 值: videoUrl },
			背景音乐URL: { 来源: 'digitalHumanRightOption.choose_music', 值: bgmiAudioUrl },
			右侧面板数据: rightPanelData,
			数字人右侧选项: digitalHumanRightOption
		});

		// 根据模式获取文本内容
		let textContent = "";
		if (modeType === 'aduio_captions') {
			// 音频驱动模式 - 优先使用提取的文本，其次使用音频JSON中的字幕文本
			const extractedText = digitalHumanRightOption.aduio_data?.textarea || "";
			const audioJsonText = digitalHumanRightOption.audioJson?.wav_text || "";
			textContent = extractedText || audioJsonText || "音频驱动模式";

			console.log('🔍 音频驱动文本内容处理:', {
				提取的文本: extractedText,
				音频JSON文本: audioJsonText,
				最终使用文本: textContent,
				音频URL是否存在: !!audioJson.wav_url
			});
		} else {
			// 输入文本模式 - 使用原有逻辑
			textContent = audioJson.wav_text || audioJson.tts.text[0] || "默认文本";
		}

		// 🔧 获取右侧操作面板的完整配置数据
		let commonJsonData = {};
		try {
			// editorData.rightPanelData 已经是通过 getData() 方法获取的数据对象
			if (editorData?.rightPanelData) {
				commonJsonData = editorData.rightPanelData;
				console.log('✅ 成功获取右侧操作面板数据用于commonJson:', commonJsonData);
			} else {
				console.warn('⚠️ 右侧操作面板数据为空，commonJson将为空对象');
			}
		} catch (error) {
			console.error('❌ 获取右侧操作面板数据失败:', error);
			commonJsonData = {};
		}

		// 🎭 添加第二层数字人图片URL、字幕数组和字体样式信息到commonJson
		try {
			// 获取第二层数字人图片URL
			const secondDigitalHumanUrl = editorData?.digitalHumanConfig?.url || '';

			// 🎨 获取字幕样式配置信息
			const subtitleConfig = editorData?.subtitleConfig || {};

			// 🔧 字体大小缩放处理（与subtitleConfigJson保持一致的缩放逻辑）
			let fontStyleFontSize = subtitleConfig.fontSize || 18;

			// 🎯 应用相同的字体大小缩放逻辑
			const originalPageSize = standardPositionsData.previewWindow?.originalPageSize;
			if (originalPageSize && originalPageSize.width > 0 && originalPageSize.height > 0) {
				// 🔧 获取动态标准高度
				const standardHeight = aspectRatio === '16:9' ? 1080 : 1920;
				const scaleY = standardHeight / originalPageSize.height;
				fontStyleFontSize = Math.round(fontStyleFontSize * scaleY);
			}

			const fontStyleInfo = {
				fontFamily: subtitleConfig.fontFamily || '1',        // 字体ID
				fontName: subtitleConfig.fontName || '微软雅黑',      // 字体名称
				fontUrl: subtitleConfig.fontUrl || '',               // 字体TTF文件URL
				fontSize: fontStyleFontSize,                         // 🔧 修改：直接使用配置的字体大小
				textColor: subtitleConfig.textColor || '#ffffff',    // 文字色
				borderColor: (subtitleConfig.borderColor === '#000000') ? '' : (subtitleConfig.borderColor || ''),       // 🔧 修复：过滤旧默认值
				borderWidth: (subtitleConfig.borderWidth === 7 || subtitleConfig.borderWidth === 12) ? 0 : (subtitleConfig.borderWidth || 0)         // 🔧 修复：过滤旧默认值
			};

			// 在commonJson中添加第二层数字人图片URL字段、字幕数组、字体样式信息和画布比例
			if (commonJsonData && typeof commonJsonData === 'object') {
				commonJsonData.secondDigitalHumanUrl = secondDigitalHumanUrl;
				// 🎬 新增：将字幕JSON数组添加到commonJson中（用户要求只加到commonJson，不加到audioJson）
				commonJsonData.subtitle_json = subtitleJsonArray.length > 0 ? subtitleJsonArray : [];
				// 🎨 新增：将字体样式信息添加到commonJson中
				commonJsonData.fontStyle = fontStyleInfo;
				// 📐 新增：将画布比例信息添加到commonJson中（用于编辑模式回显）
				commonJsonData.aspectRatio = editorData?.aspectRatio || '9:16';
				commonJsonData.leftOperateConfigData = leftPanelData.leftOperateConfigData;
				console.log('✅ 已添加第二层数字人图片URL、字幕数组、字体样式和画布比例到commonJson:', {
					secondDigitalHumanUrl: secondDigitalHumanUrl,
					subtitle_json: commonJsonData.subtitle_json,
					字幕数组长度: commonJsonData.subtitle_json.length,
					fontStyle: commonJsonData.fontStyle,
					aspectRatio: commonJsonData.aspectRatio,
					字体信息: {
						字体ID: fontStyleInfo.fontFamily,
						字体名称: fontStyleInfo.fontName,
						字体URL: fontStyleInfo.fontUrl,
						字号: fontStyleInfo.fontSize
					},
					来源: editorData?.digitalHumanConfig ? '编辑器数字人配置' : '默认空值'
				});
			}
		} catch (error) {
			console.error('❌ 添加第二层数字人图片URL、字幕数组和字体样式到commonJson失败:', error);
		}

		// 构建完整的保存参数 - 数字人视频生成API所需的完整参数对象
		const saveParams = {
			// 基础信息参数
			title: title, // 作品标题 - 来源：编辑器标题输入或默认"未命名作品"，用于标识和管理生成的视频作品
			userId: userId, // 用户ID - 来源：登录状态管理，用于关联用户账户和权限验证
			digitalHumanId: personJson.id, // 数字人ID - 来源：左侧面板选择的数字人，用于视频生成时加载对应的数字人模型

			// 核心配置参数 - 控制视频生成的主要元素
			personJson: personJson, // 数字人配置对象 - 包含数字人的位置、尺寸、类型等信息，用于在视频中正确渲染数字人
			audioJson: audioJson, // 音频配置对象 - 包含TTS参数、音频文件URL、字幕文本等，用于生成或使用音频轨道
			bgJson: bgJson, // 背景配置对象 - 包含背景图片URL和位置尺寸信息，用于渲染视频背景层
			subtitleConfigJson: subtitleConfigJson, // 字幕配置对象 - 包含字幕样式、位置、显示状态等，用于渲染字幕层

			// 内容参数
			text: textContent, // 文本内容 - 来源：输入文本模式的用户输入或音频驱动模式的识别文本，用于TTS合成或字幕显示
			bgColor: bgColor, // 背景颜色 - 来源：左侧面板纯色背景选择，用于纯色背景模式的视频渲染

			// 技术参数 - 控制视频输出规格和资源
			screenWidth: screenWidth, // 屏幕宽度 - 来源：系统屏幕分辨率或编辑器配置，用于确定视频输出宽度
			screenHeight: screenHeight, // 屏幕高度 - 来源：系统屏幕分辨率或编辑器配置，用于确定视频输出高度
			videoUrl: videoUrl, // 视频URL - 来源：音频文件URL（与audioJson.wav_url保持一致），用于音频驱动模式的视频合成
			bgmiAudioUrl: bgmiAudioUrl, // 背景音乐URL - 来源：右侧面板背景音乐选择，用于为视频添加背景音乐轨道

			// 扩展配置参数
			commonJson: commonJsonData // 右侧操作面板完整配置数据 - 包含音频设置、字幕配置、背景音乐等详细参数，用于保存用户的完整编辑状态和高级配置选项
		};

		console.log('🔧 构建的保存参数:', saveParams);
		console.log('✅ 数字人ID参数已添加:', {
			digitalHumanId: saveParams.digitalHumanId,
			'personJson.id': saveParams.personJson.id,
			'ID来源': leftPanelData.digitalHuman?.id ? '左侧面板' : (editorData?.digitalHumanConfig?.id ? '编辑器配置' : '默认值')
		});
		console.log('🔧 commonJson字段已添加:', {
			'commonJson存在': !!saveParams.commonJson,
			'commonJson类型': typeof saveParams.commonJson,
			'commonJson内容': saveParams.commonJson,
			'commonJson键数量': saveParams.commonJson ? Object.keys(saveParams.commonJson).length : 0,
			'🎭 第二层数字人URL': saveParams.commonJson?.secondDigitalHumanUrl || '无'
		});
		console.log('✅ 音频驱动模式数据应用完成:', {
			是否为音频驱动: modeType === 'aduio_captions',
			'🎵 音频轨道数据': {
				音频URL: saveParams.audioJson.wav_url,
				音频名称: saveParams.audioJson.wav_name,
				音频音量: saveParams.audioJson.volume,
				音频时长: saveParams.audioJson.duration
			},
			'📝 字幕轨道数据': {
				文本内容: saveParams.text,
				字幕开关: saveParams.subtitleConfigJson.show,
				字幕文本: saveParams.audioJson.wav_text
			},
			'🎬 完整audioJson': saveParams.audioJson
		});
		console.log('🎬 字幕JSON数组处理完成:', {
			字幕数组长度: subtitleJsonArray.length,
			字幕数组样例: subtitleJsonArray[0] || '无数据',
			传递到audioJson: !!saveParams.audioJson.subtitle_json,
			完整字幕数组: subtitleJsonArray
		});
		return saveParams;
	} catch (error) {
		console.error('❌ 构建保存参数失败:', error);
		throw error; // 重新抛出错误，让调用方处理
	}
};

// 保存数字人作品的处理方法
const handleSaveDigitalWork = async () => {
	if (isGeneratingVideo.value) {
		return; // 如果正在生成，直接返回
	}

	isGeneratingVideo.value = true; // 开始生成，设置加载状态

	try {
		// 检查是否为数字人编辑器页面
		if (route.name !== 'DigitalHumanEditorPage') {
			console.warn('当前不在数字人编辑器页面，无法保存作品');
			return;
		}

		// 🔒 数字人会员过期检查 - 在所有其他检查之前进行
		console.log('🔒 开始进行数字人会员过期检查...');

		try {
			// 从 Pinia store 中获取用户数字人会员信息
			const digitalHumanEndTime = loginStore.memberInfo?.digital_human?.end_time;

			if (digitalHumanEndTime) {
				// 获取当前时间戳
				const currentTime = Date.now();

				// 将 end_time 转换为时间戳进行比较
				// end_time 格式通常为 "2025-01-01 23:59:59"
				const endTimeTimestamp = new Date(digitalHumanEndTime).getTime();

				console.log('🔒 数字人会员信息检查:', {
					endTime: digitalHumanEndTime,
					endTimeTimestamp: endTimeTimestamp,
					currentTime: currentTime,
					isExpired: endTimeTimestamp <= currentTime
				});

				// 如果 end_time 小于等于当前时间，则判定为已过期
				if (endTimeTimestamp <= currentTime) {
					console.log('❌ 数字人会员已过期，禁止生成视频');
					ElMessage.error('数字人会员已过期');
					return; // 阻止继续执行生成视频的操作
				}

				console.log('✅ 数字人会员未过期，继续执行生成视频流程');
			} else {
				console.log('⚠️ 未获取到数字人会员信息，跳过会员过期检查');
			}
		} catch (memberCheckError) {
			console.error('❌ 数字人会员过期检查失败:', memberCheckError);
			ElMessage.error('会员状态检查失败，请稍后重试');
			return; // 阻止继续执行
		}

		console.log('🎬 开始获取数字人编辑器数据...');

		// 通过事件总线请求数字人编辑器页面的数据
		const editorData = await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error('获取编辑器数据超时'));
			}, 5000); // 5秒超时

			eventBus.emit('request-digital-human-data', (data) => {
				clearTimeout(timeout);
				resolve(data);
			});
		});

		if (!editorData) {
			throw new Error('无法获取数字人编辑器数据');
		}

		console.log('📊 成功获取编辑器数据:', editorData);

		// 构建保存参数
		const saveParams = buildSaveParams(editorData);

		console.log("正在保存数字人作品...", saveParams);

		// 🔒 时长权限检查 - 在调用生成视频接口之前进行验证
		console.log('🔒 开始进行数字人时长权限检查...');

		// 获取数字人视频时长（秒数）
		let videoDurationInSeconds = 0;

		// 优先从digitalHumanStore获取时长
		if (digitalHumanStore.totalDuration && digitalHumanStore.totalDuration > 0) {
			videoDurationInSeconds = digitalHumanStore.totalDuration;
			console.log('🎬 从digitalHumanStore获取视频时长:', videoDurationInSeconds, '秒');
		}
		// 备选方案：从saveParams的audioJson中获取时长
		else if (saveParams.audioJson && saveParams.audioJson.duration && saveParams.audioJson.duration > 0) {
			videoDurationInSeconds = Math.ceil(saveParams.audioJson.duration);
			console.log('🎬 从audioJson获取视频时长:', videoDurationInSeconds, '秒');
		}
		// 如果都没有，使用默认最小时长
		else {
			videoDurationInSeconds = 10; // 默认10秒
			console.log('⚠️ 未获取到有效时长，使用默认时长:', videoDurationInSeconds, '秒');
		}

		// 调用时长权限检查接口
		const userId = getUserId();
		console.log('🔒 调用时长权限检查接口:', {
			userId: userId,
			feat: "digital_time",
			need: videoDurationInSeconds
		});

		try {
			const permissionResponse = await checkUploadPermission({
				userId: userId,
				feat: "digital_time",
				need: videoDurationInSeconds
			});

			console.log('🔒 时长权限检查结果:', permissionResponse);

			// 检查权限验证结果
			if (permissionResponse && permissionResponse.content && permissionResponse.content.result === false) {
				console.log('❌ 数字人时长权限不足，禁止生成视频');
				ElMessage.error('您的剩余合成时长已不足，请先购买数字人套餐');
				return; // 阻止继续执行
			}
		
			console.log('✅ 数字人时长权限检查通过，继续生成视频');
		} catch (permissionError) {
			console.error('❌ 时长权限检查失败:', permissionError);
			ElMessage.error('权限检查失败，请稍后重试');
			return; // 阻止继续执行
		}

		// � 音频URL验证 - 确保用户已生成音频
		console.log('🎵 开始进行音频URL验证...');

		const videoUrl = saveParams.videoUrl;
		console.log('🔍 音频URL验证详情:', {
			videoUrl: videoUrl,
			类型: typeof videoUrl,
			长度: videoUrl ? videoUrl.length : 0,
			是否为空字符串: videoUrl === '',
			是否为null: videoUrl === null,
			是否为undefined: videoUrl === undefined
		});

		// 检查音频URL是否有效
		if (!videoUrl || videoUrl.trim() === '') {
			console.log('❌ 音频URL验证失败：音频URL为空或无效');
			ElMessage.error('请先生成音频！');
			isGeneratingVideo.value = false; // 重置加载状态
			return; // 阻止继续执行
		}

		console.log('✅ 音频URL验证通过，音频URL有效:', videoUrl);

		// 🎯 根据作品状态选择不同的接口
		let response;

		// 检查是否为失败场景：通过原始作品数据的状态判断
		const isFailedWork = isEditMode.value && editorData?.originalWorkData?.status === '2'; // 状态'2'表示失败

		if (isFailedWork) {
			// 失败场景：调用editDigitalWork接口重新生成
			const editParams = {
				id: parseInt(route.query.id),
				...saveParams
			};
			console.log("❌ 失败重试：调用editDigitalWork接口", {
				作品ID: route.query.id,
				作品状态: editorData?.originalWorkData?.status,
				参数总数: Object.keys(editParams).length,
				包含字段: Object.keys(editParams)
			});
			response = await editDigitalWork(editParams);
		} else {
			// 成功场景和新建场景：都调用saveDigitalWork接口
			const isNewWork = !isEditMode.value;
			console.log(isNewWork ? "🆕 新建模式：调用saveDigitalWork接口" : "✅ 成功编辑：调用saveDigitalWork接口", {
				场景类型: isNewWork ? '新建作品' : '成功作品编辑',
				作品ID: route.query.id || '无',
				作品状态: editorData?.originalWorkData?.status || '新建',
				参数总数: Object.keys(saveParams).length,
				包含字段: Object.keys(saveParams)
			});
			response = await saveDigitalWork(saveParams);
		}

		if (response && response.success) {
			// 保存成功的处理
			console.log("数字人作品保存成功:", response);
			proxy.$message.success('视频生成成功！');

			// API调用成功后自动跳转到数字人中转页面
			try {
				console.log('🚀 视频生成成功，正在跳转到数字人中转页面...');
				router.push({ path: '/digital-human-transition' });
			} catch (routerError) {
				console.error('跳转到数字人中转页面失败:', routerError);
				// 跳转失败不影响主流程，仅记录错误
			}
		} else {
			// 保存失败的处理
			console.error("数字人作品保存失败:", response);
			proxy.$message.error('视频生成失败，请重试！');
		}
	} catch (error) {
		console.error("保存数字人作品时发生错误:", error);
		proxy.$message.error('视频生成过程中发生错误，请重试！');
	} finally {
		isGeneratingVideo.value = false; // 无论成功失败，都重置加载状态
	}
};
let tooltipRef = ref(null)
let onMouseEnter = () => {
	console.log('onMouseEnter');
	// loginStore.userInfo=null
	// loginStore.memberInfo=null;
	// 弹窗内容变化后，刷新定位
	tooltipRef.value?.updatePopper()
	isTooltipVisible.value = true
}
let buy_digital_dialog_ref=ref(null)//数字人弹窗
let buy_dub_dialog_ref=ref(null)//AI配音弹窗
let buy_business_dialog_ref=ref(null)//AI商配弹窗
let buy_thail = () => {
	if (!loginStore.token) {
		proxy.$modal.open('组合式标题');
		return
	}
	  let url = `${window.location.origin}/membership`
	switch (route.name) {
		case 'AIDubbing':
			// window.open(url, '_blank');
			buy_dub_dialog_ref.value.dialogVisible=true
			break;
		case 'commercialDubbing':
			// url = `${url}?nav=business`;
			buy_business_dialog_ref.value.dialogVisible=true
			break;
		case 'ContentCreation':
			url = `${url}?nav=calculate`;
			window.open(url, '_blank');
			break;
		case 'DigitalHumanTransition':
		case 'DigitalHumanEditorPage':
			buy_digital_dialog_ref.value.dialogVisible=true
			break;
		default:
			break;
	}
}
provide('accountInfoClick', accountInfoClick)
provide('redemption_success', redemption_success)
provide('creator_community', creator_community)
provide('loginout', loginout)
provide('redemption_code', redemption_code)
provide('remove_account', remove_account)
provide('welfare_center', welfare_center)
</script>

<!-- 头部header  -->
<template>
	<div class="action-container flex flex_j_c-flex-end flex_a_i-center height-full">
		<!-- 联系我们 -->
		<el-popover trigger="hover" width="188px">
			<template #default>
				<customerService></customerService>
			</template>
			<template #reference>
				<span class="cursor-pointer contactUs">
					<img src="@/assets/img/contact.png" class="width-18 height-18" alt="">
					联系我们
				</span>
			</template>
		</el-popover>
		<span class="cursor-pointer contactUs buy_thail" v-if="['commercialDubbing', 'AIDubbing', 'ContentCreation', 'DigitalHumanEditorPage'].includes(route.name)" @click="buy_thail">
			<img src="@/assets/img/buy_thail.svg" class="width-18 height-18" alt="">
			购买套餐
		</span>
		<!-- 使用帮助按钮 - 在数字人页面中隐藏 -->
		<el-tooltip v-if="!isDigitalHumanPage" class="box-item" effect="dark" content="使用帮助" placement="bottom"
			append-to="#app">
			<!-- <Iconfont
        class="margin_r-12 cursor-pointer"
        size="20px"
        name="wenhao"
        @click="use_help"
       /> -->
			<span class="cursor-pointer margin-n-12 used_help"
				:class="route.meta.header_module == 'dark' ? 'helpDark' : ''" @click="go_help">
				<img :src="route.meta.header_module == 'dark' ? helpDark : helpLight" class="width-18 height-18 "
					alt="">
				使用帮助
			</span>
		</el-tooltip>

		<!-- 福利中心按钮 - 在数字人页面中隐藏 -->
		<el-tooltip v-if="!isDigitalHumanPage" class="box-item" effect="dark" content="福利中心" placement="bottom"
			append-to="#app">
			<span class="cursor-pointer margin_r-12 welfare_center_btn"
				:class="route.meta.header_module == 'dark' ? 'helpDark' : ''" @click="welfare_center">
				<svg width="18" height="18" viewBox="0 0 24 24" fill="none" class="margin_r-6">
					<path d="M20 12V22H4V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
						stroke-linejoin="round" />
					<path d="M22 7H2L4 2H20L22 7Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
						stroke-linejoin="round" />
					<path d="M12 22V7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
						stroke-linejoin="round" />
				</svg>
				福利中心
			</span>
		</el-tooltip>

		<template v-if="route.name == 'commercialDubbing' && loginStore.token">
			<button class="commercialDubbing_tip_button margin_r-12" @click="go_tryListening">
				合成字符数 <span>{{ getComputing('composite') }}</span>
			</button>
			<button class="commercialDubbing_tip_button margin_r-12" @click="go_tryListening">
				试听字符数 <span>{{ getComputing('listening') }}</span>
			</button>
		</template>
		<!-- 数字人页面专用的生成视频按钮 -->
		<template v-if="route.name == 'DigitalHumanEditorPage' && loginStore.token">
			<button class="generate-video-btn margin_r-24 margin_l-12" :class="{ 'generating': isGeneratingVideo }"
				:disabled="isGeneratingVideo" @click="handleGenerateVideoClick">
				<span v-if="isGeneratingVideo">生成中...</span>
				<span v-else>生成视频</span>
			</button>
		</template>

		<template
			v-if="(route.name == 'ContentCreation' || route.name == 'VoiceOver' || route.name == 'MusicAudio' || route.name == 'VideoEditing' || route.name == 'DigitalHumanEditorPage') && loginStore.token">
			<img v-if="route.name == 'DigitalHumanEditorPage'" :src="isDue ? isDueImg[1] : isDueImg[0]" class="width-25 margin_l-12" alt="">
			<img v-else src="@/assets/images/account/character.svg" class="width-20 height-20 margin_l-12" alt="">
			<div v-if="route.name == 'DigitalHumanEditorPage'" class="digit  margin_r-12 margin_l-4">
				{{ remainMinutes }}
			</div>
			<div v-else class="digit  margin_r-12 margin_l-4">
				{{ getComputing('calculate') }}
			</div>
		</template>
		<!-- 你好 -->

		<!--    <Iconfont-->
		<!--        class="margin_r-15 cursor-pointer"-->
		<!--        size="20px"-->
		<!--        name="xiaoxi"-->
		<!--    />-->
		<!--  登录按钮  -->
		<!-- :visible="isTooltipVisible" -->
		<div class="header_login margin_l-16">
			<div class="login_button cursor-pointer margin_r-5" v-if="!loginStore.token" @click="loginButton">登录</div>
			<el-tooltip popper-class="information-tooltip" effect="light" placement="top" append-to="#app"
				:popper-options="popperOptions" ref="tooltipRef" v-else>
				<template #content>
					<information />
				</template>

				<el-avatar class="cursor-pointer" :size="34">
					<div class="avatar_img" @mouseenter="onMouseEnter">
						<img class="avatar_img_avatar"
							:src="loginStore.userInfo && loginStore.userInfo.avatar && loginStore.userInfo.avatar != '' ? loginStore.userInfo.avatar : avatar"
							alt="">
						<div class="avatar_img_sign">
							<img :src="signImg" alt="">
						</div>

					</div>
				</el-avatar>
			</el-tooltip>

		</div>
		<!--    <el-avatar class="cursor-pointer" :size="34" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />-->
		<!--    <div class="characters_style margin_l-5 cursor-pointer">认证享权益</div>-->
		<!--    <el-tooltip-->
		<!--      content="刷新页面"-->
		<!--      placement="bottom"-->
		<!--      :show-after="500">-->
		<!--      <Iconfont-->
		<!--        class="margin_r-15 cursor-pointer"-->
		<!--        size="16px"-->
		<!--        name="refresh"-->
		<!--       />-->
		<!--    </el-tooltip>-->
		<!--    <el-tooltip-->
		<!--      content="主题设置"-->
		<!--      placement="bottom"-->
		<!--      :show-after="500">-->
		<!--      <Iconfont-->
		<!--        class="margin_r-15 cursor-pointer"-->
		<!--        size="16px"-->
		<!--        name="set"-->
		<!--        @click="iconfontClickHandle('setting')" />-->
		<!--    </el-tooltip>-->
		<!--    <el-tooltip-->
		<!--      content="清理缓存"-->
		<!--      placement="bottom"-->
		<!--      :show-after="500">-->
		<!--      <Iconfont-->
		<!--        class="margin_r-15 cursor-pointer"-->
		<!--        size="16px"-->
		<!--        name="clear"-->
		<!--        @click="iconfontClickHandle('clear')" />-->
		<!--    </el-tooltip>-->
		<!--    <el-tooltip-->
		<!--      content="暗黑模式"-->
		<!--      placement="bottom"-->
		<!--      :show-after="500">-->
		<!--      <el-switch-->
		<!--        class="margin_r-15"-->
		<!--        v-model="mode"-->
		<!--        :active-value="ThemeMode.DARK"-->
		<!--        :inactive-value="ThemeMode.LIGHT"-->
		<!--        inline-prompt-->
		<!--        active-text="黑"-->
		<!--        inactive-text="亮"-->
		<!--        active-color="#222222" />-->
		<!--    </el-tooltip>-->
		<!--    <Notice />-->
		<!--    <el-dropdown trigger="click" @command="dropdownHandle">-->
		<!--      <el-avatar-->
		<!--        class="cursor-pointer"-->
		<!--        :src="avatar">-->
		<!--        {{ name }}-->
		<!--      </el-avatar>-->
		<!--      <template #dropdown>-->
		<!--        <el-dropdown-menu>-->
		<!--          <el-dropdown-item command="personal">个人中心</el-dropdown-item>-->
		<!--          <el-dropdown-item command="logout">退出登录</el-dropdown-item>-->
		<!--        </el-dropdown-menu>-->
		<!--      </template>-->
		<!--    </el-dropdown>-->
		<!--    <Theme ref="refTheme" v-if="visible" />-->
		<!-- 账号信息弹窗 -->
		<accountInfo ref="account_info_ref"></accountInfo>
		<!-- 创作者社群弹窗 -->
		<creatorCommunity ref="creator_community_ref"></creatorCommunity>
		<!-- 退出登录弹窗 -->
		<loginOut ref="login_out_ref"></loginOut>
		<redemptionCode ref="redemption_code_ref"></redemptionCode>
		<redemptionSuccess ref="redemption_success_ref"></redemptionSuccess>
		<!-- 注销账号 -->
		<removeAccount ref="remove_account_ref"></removeAccount>
		<welfareCenterDialog ref="welfare_center_ref"></welfareCenterDialog>
		<!-- 生成视频确认弹窗 -->
		<div v-if="showGenerateVideoDialog" class="confirmation-dialog-overlay" @click.self="closeConfirmationDialog">
			<div class="confirmation-dialog">
				<h3 class="dialog-title">
					配音帮手平台用户声明
					<br>
					<span class="dialog-subtitle">（以下简称"平台"）</span>
				</h3>
				<div class="dialog-content">
					本声明旨在协助您高效使用【配音帮手平台】的工具服务，规范作品上传与管理行为。当您上传或生成作品时，即视为已充分理解并完全接受以下条款：

					第一条 知识产权保证
					作为平台用户，您须确保上传或发布的作品满足以下要求：

					独立权属：作品需由您本人创作或合法取得授权；

					无侵权风险：作品不得侵犯任何第三方的知识产权、肖像权、隐私权等合法权益。

					第二条 合法合规义务
					在使用平台服务过程中，您承诺：

					严守法律底线：遵守中国法律法规，维护国家安全与社会公共利益；

					内容合规性：作品不得含有虚假信息，禁止传播暴力、淫秽、歧视等违法或违背公序良俗的内容；

					违规责任：若违反上述规定，平台有权根据违规严重程度采取包括但不限于以下措施：

					删除违规内容
					下架相关作品
					限制账号功能（如禁止发布、封禁账号）

					执法配合：若涉嫌违法犯罪，平台将依法向监管或公安机关移交证据并配合调查。

					第三条 侵权投诉处理
					如您上传的作品包含以下侵权素材：

					创意、文本、肖像、音频、图片、视频等
					平台在收到有效侵权投诉后，有权依据平台规则及相关法律：

					对涉嫌侵权作品采取临时下架或永久删除；

					对用户账号实施警告、功能限制或封禁等措施。
				</div>
				<div class="dialog-buttons">
					<button class="cancel-btn" @click="closeConfirmationDialog">取消</button>
					<button class="confirm-btn" @click="confirmGeneration">
						我已知晓，同意
					</button>
				</div>
			</div>
		</div>
	</div>
	<!--购买数字人弹窗-->
	<buyDigitalDialog ref="buy_digital_dialog_ref"></buyDigitalDialog>
	<!--购买单配会员弹窗 -->
	<buyDubDialog ref="buy_dub_dialog_ref"></buyDubDialog>
	<!--购买商配弹窗 -->
	<buyBusinessDialog ref="buy_business_dialog_ref"></buyBusinessDialog>
</template>

<style lang="scss" scoped>
.font-color {
	color: var(--main-page-color);
}

.action-container {
	height: 50px;
	/* 设置固定高度 */
	padding: 0;
	/* 添加左右padding */
	box-shadow: none;
	/* 确保没有阴影 */
	margin-right: 20px;
	background-color: #FFFFFF;

	.used_help {
		display: flex;
		align-items: center;

		img {
			margin-right: 9px;
		}

		&.helpDark {
			color: #fff;
		}
	}

	.contactUs {
		display: flex;
		align-items: center;
		margin-right: 5px;
		padding-right: 10px;
		img {
			margin-right: 9px;
		}
		&.buy_thail{
			padding-right: 13px;
		}
	}

	.iconfont {
		// margin-right: 15px;
	}

	.digit {
		font-size: 16px;
		color: #333333;
	}

	.characters_style {
		color: #000;
		font-size: 14px;
	}

	.header_login {
		padding-left: 32px;
		position: relative;

		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 18px;
			background-color: rgba(0, 0, 0, 0.1);
		}
	}

	//登录按钮样式
	.login_button {
		//margin-left: 24px;
		width: 88px;
		height: 32px;
		font-size: 14px;
		font-weight: 600;
		//font-family: HarmonyOS_Sans_SC;
		color: #FFFFFF;
		background-color: #0AAF60;
		border-radius: 4px;
		display: flex;
		align-items: center;
		justify-content: center;
		//line-height: 24px;
		cursor: pointer;

		&:hover {
			opacity: 0.9;
		}
	}

	.synthesis {
		width: 74px;
		height: 20px;
		margin-right: 4px;
	}

	.synthesis_label {
		font-size: 16px;
		line-height: 20px;
		color: #333333;
	}

	.commercialDubbing_tip_button {
		box-sizing: border-box;
		padding: 6px 12px;
		/* 灰色按钮 */
		border: 1px solid #D3D3D2;
		border-radius: 20px;
		display: flex;
		color: #353D49;
		line-height: 20px;
		font-size: 14px;
		background-color: transparent;

		span {
			margin-left: 9px;
			// font-style: italic;
			font-weight: 400;
			font-size: 16px;
			font-weight: bold;
		}
	}

	.welfare_center_btn {
		display: flex;
		align-items: center;

		&.helpDark {
			color: #fff;
		}
	}

	// 数字人页面专用生成视频按钮样式
	.generate-video-btn {
		width: 84px;
		height: 32px;
		border: 1px solid #0AAF60;
		border-radius: 3px;
		background-color: #0AAF60;
		color: #fff;
		font-size: 14px;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;

		// &:hover:not(:disabled) {
		// 	border-color: #0AAF60;
		// 	color: #0AAF60;
		// }

		&:disabled {
			cursor: not-allowed;
			opacity: 0.6;
		}

		&.generating {
			// border-color: #0AAF60;
			// color: #0AAF60;
			// background-color: rgba(10, 175, 96, 0.1);
		}
	}
}

.el-avatar {
	background-color: transparent;

	.avatar_img {
		width: 28px;
		height: 28px;
		border-radius: 50%;
		position: relative;

		.avatar_img_avatar {
			width: 100%;
			height: 100%;
			border-radius: 50%;
		}

		.avatar_img_sign {
			position: absolute;
			right: 0;
			bottom: -2px;
			z-index: 1;
			box-sizing: border-box;
			width: 10px;
			height: 10px;

			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			img {
				width: 10px;
				height: 9px
			}

			&.ordinary {
				border: 2px solid #FFFFFF;
				background: #68CD5F;
			}

		}
	}
}

.confirmation-dialog-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: flex-start;
	/* Aligns to the top */
	z-index: 2000;
	/* Make sure it's on top of other content */
}

.confirmation-dialog {
	width: 420px;
	margin-top: 30px;
	/* Distance from top */
	background-color: white;
	border-radius: 8px;
	padding: 24px 16px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	max-height: 80vh;
}

.dialog-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin: 0;
	text-align: left;
	line-height: 1.5;
}

.dialog-subtitle {
	font-size: 12px;
	font-weight: normal;
	color: #666;
}

.dialog-content {
	margin-top: 10px;
	font-size: 14px;
	color: #1E1E1E;
	line-height: 1.5;
	text-align: left;
	/* To handle long text */
	white-space: pre-wrap;
	overflow-y: auto;
	flex-grow: 1;
}

.dialog-buttons {
	margin-top: 32px;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.cancel-btn {
	width: 96px;
	height: 40px;
	background-color: #D3D3D2;
	color: #FFFFFF;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-size: 14px;
}

.confirm-btn {
	width: 163px;
	height: 40px;
	background-color: rgba(10, 175, 96, 0.75);
	color: #FFFFFF;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	margin-left: 12px;
	font-size: 14px;
	transition: background-color 0.3s;
}

.confirm-btn:disabled {
	background-color: #CCCCCC;
	cursor: not-allowed;
}
</style>
<style lang="scss">
.information-tooltip {
	padding: 0;
	box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.12);
	border-radius: 12px;
	overflow: hidden;

	.el-popper__arrow {
		display: none;
		/* 确保箭头被隐藏 */
	}
}

.dark {
	.action-container {
		.digit {
			color: #fff;
		}
	}
}
</style>
